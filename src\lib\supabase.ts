// Supabase Configuration
// Reason for Configuration: Centralized Supabase client setup for database operations
// Task Performed: Creates and exports Supabase client instance for file storage and database operations
// Linking Information: Internal - Used throughout the application for database and storage operations

import { createClient } from '@supabase/supabase-js';

// Supabase project configuration
const supabaseUrl = 'https://mvfmyzhxynhgspfmonox.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im12Zm15emh4eW5oZ3NwZm1vbm94Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM1MTQ2ODgsImV4cCI6MjA2OTA5MDY4OH0.0webiBBGGp_GMhS4wOmwqGSIZ2YcowP6mrpnqitckR4';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database Types
// Reason for Types: Type safety for database operations and form data
// Task Performed: Defines TypeScript interfaces for database tables and operations
// Linking Information: Internal - Used by components for type-safe database operations

export interface AttachmentRecord {
  id?: number;
  client_name: string;
  file_name: string;
  file_size: number;
  file_type: string;
  file_url: string;
  storage_path: string;
  uploaded_at?: string;
  created_at?: string;
  updated_at?: string;
}

// File Upload Service
// Reason for Service: Centralized file upload functionality with Supabase Storage
// Task Performed: Handles file uploads to Supabase Storage and database record creation
// Linking Information: Internal - Used by OrderFormPopup component for file upload operations

export class FileUploadService {
  
  // Upload File to Supabase Storage
  // Reason for Function: Upload files to Supabase Storage bucket
  // Task Performed: Uploads file to storage and returns public URL
  // Linking Information: Internal - Used by uploadAttachment method
  static async uploadFileToStorage(file: File, clientName: string): Promise<string> {
    const fileExt = file.name.split('.').pop();
    const fileName = `${clientName}_${Date.now()}.${fileExt}`;
    const filePath = `attachments/${fileName}`;

    const { data, error } = await supabase.storage
      .from('attachments')
      .upload(filePath, file);

    if (error) {
      throw new Error(`File upload failed: ${error.message}`);
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('attachments')
      .getPublicUrl(filePath);

    return publicUrl;
  }

  // Upload Attachment with Database Record
  // Reason for Function: Complete file upload process with database record creation
  // Task Performed: Uploads file to storage and creates database record
  // Linking Information: Internal - Used by OrderFormPopup component for complete file upload
  static async uploadAttachment(file: File, clientName: string): Promise<AttachmentRecord> {
    try {
      // Upload file to storage
      const fileUrl = await this.uploadFileToStorage(file, clientName);
      
      // Create database record
      const attachmentData: Omit<AttachmentRecord, 'id' | 'uploaded_at' | 'created_at' | 'updated_at'> = {
        client_name: clientName,
        file_name: file.name,
        file_size: file.size,
        file_type: file.type,
        file_url: fileUrl,
        storage_path: `attachments/${clientName}_${Date.now()}.${file.name.split('.').pop()}`
      };

      const { data, error } = await supabase
        .from('attachments')
        .insert([attachmentData])
        .select()
        .single();

      if (error) {
        throw new Error(`Database insert failed: ${error.message}`);
      }

      return data as AttachmentRecord;
    } catch (error) {
      console.error('Upload attachment error:', error);
      throw error;
    }
  }

  // Get Attachments by Client Name
  // Reason for Function: Retrieve all attachments for a specific client
  // Task Performed: Queries database for client attachments
  // Linking Information: Internal - Used for retrieving client attachment URLs
  static async getAttachmentsByClient(clientName: string): Promise<AttachmentRecord[]> {
    const { data, error } = await supabase
      .from('attachments')
      .select('*')
      .eq('client_name', clientName)
      .order('uploaded_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch attachments: ${error.message}`);
    }

    return data as AttachmentRecord[];
  }
}
